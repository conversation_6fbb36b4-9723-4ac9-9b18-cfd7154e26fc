-- Agregar campo remitente_interno_id a la tabla radicados para Issue #10
-- Este campo almacena el UsuarioSeccion que actúa como remitente en radicados de envío

ALTER TABLE radicados 
ADD COLUMN IF NOT EXISTS remitente_interno_id UUID;

-- Agregar foreign key constraint
ALTER TABLE radicados 
ADD CONSTRAINT FK_RADICADO_REMITENTE_INTERNO 
FOREIGN KEY (remitente_interno_id) REFERENCES usuarios_secciones (id);

-- Agregar comentario para documentar el propósito del campo
COMMENT ON COLUMN radicados.remitente_interno_id IS 'ID del UsuarioSeccion que actúa como remitente en radicados de envío (Issue #10)';
