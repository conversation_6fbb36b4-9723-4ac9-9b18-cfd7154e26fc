package co.com.gedsys.base.application.usecase.produccion.radicacion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.AnexoDocumentoDTO;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.PropiedadRadicadoDTO;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoRecibidoCommand;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import co.com.gedsys.base.domain.documento.Anexo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.metadato.Metadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.radicado.PropiedadesRadicado;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RadicarDocumentoRecibidoUseCase implements UseCase<RadicarDocumentoRecibidoCommand, RadicadoDTO> {

    private final DocumentoRepository documentoRepository;
    private final TipoDocumentalRepository tipoDocumentalRepository;
    private final UnidadDocumentalRepository unidadDocumentalRepository;
    private final DefinicionMetadatosRepository definicionMetadatosRepository;
    private final ConsecutivoRepository consecutivoRepository;
    private final RadicadoRepository radicadoRepository;
    private final RadicadoApplicationLayerMapper radicadoMapper;
    private final ExternalUsersRepository externalUsersRepository;
    private final SeccionRepository seccionRepository;

    @Override
    @Transactional
    public RadicadoDTO execute(RadicarDocumentoRecibidoCommand input) {
        Documento documento = crearDocumento(input);
        Documento documentoGuardado = documentoRepository.save(documento);

        // Obtener entidades para correspondencia
        ExternalUser remitente = obtenerRemitente(input.remitenteId());
        Seccion destino = obtenerDestino(input.destinoId());
        UsuarioSeccion destinatarioInterno = obtenerDestinatarioInterno(input.destinatarioInternoId(), destino);

        Consecutivo consecutivo = obtenerConsecutivoRecepcion();

        // Crear el radicado con el consecutivo
        Radicado radicado = new Radicado(consecutivo);

        // Establecer las propiedades del radicado si existe
        if (input.propiedadRadicado() != null) {
            PropiedadRadicadoDTO propiedadDTO = input.propiedadRadicado();
            PropiedadesRadicado propiedades = new PropiedadesRadicado(
                    propiedadDTO.page(),
                    propiedadDTO.x(),
                    propiedadDTO.y(),
                    propiedadDTO.height(),
                    propiedadDTO.width(),
                    propiedadDTO.rotationDegrees()
            );

            // Crear un nuevo radicado con las propiedades
            radicado = new Radicado(
                    null,
                    radicado.getEstado(),
                    radicado.getNumeroRadicado(),
                    radicado.getFechaExpedicion(),
                    radicado.getEmisor(),
                    null,
                    radicado.getPrefijo(),
                    radicado.getSufijo(),
                    radicado.getTipo(),
                    propiedades,
                    null, // No hay destinatario para radicados de recepción
                    remitente,
                    destino,
                    destinatarioInterno,
                    radicado.getConsecutivo(),
                    null
            );
        }

        // Establecer campos de correspondencia en el radicado
        radicado = new Radicado(
                radicado.getId(),
                radicado.getEstado(),
                radicado.getNumeroRadicado(),
                radicado.getFechaExpedicion(),
                radicado.getEmisor(),
                radicado.getObservaciones(),
                radicado.getPrefijo(),
                radicado.getSufijo(),
                radicado.getTipo(),
                radicado.getPropiedadesRadicado(),
                radicado.getDestinatario(),
                remitente,
                destino,
                destinatarioInterno,
                radicado.getConsecutivo(),
                radicado.getDocumento()
        );

        // Validar correspondencia para recepción
        radicado.validarCorrespondenciaParaRecepcion();

        // Expedir el radicado con el documento
        radicado.expedir(documentoGuardado);

        consecutivoRepository.save(consecutivo);
        Radicado radicadoGuardado = radicadoRepository.save(radicado);

        return radicadoMapper.toDTO(radicadoGuardado);
    }

    private Documento crearDocumento(RadicarDocumentoRecibidoCommand input) {
        TipoDocumental tipoDocumental = tipoDocumentalRepository
                .findById(UUID.fromString(input.tipoDocumentalId()))
                .orElseThrow(() -> new EntityNotExistsException("Tipo documental no encontrado"));

        UnidadDocumental unidadDocumental = unidadDocumentalRepository
                .findById(UUID.fromString(input.unidadDocumentalId()))
                .orElseThrow(() -> new EntityNotExistsException("Unidad documental no encontrada"));

        Set<Metadato> metadatos = buildMetadatos(input.metadatos());

        List<Anexo> anexos = input.anexos().stream()
                .map(this::buildAnexo)
                .toList();

        return new Documento(
                input.titulo(),
                input.fileId(),
                tipoDocumental,
                unidadDocumental,
                input.autor(),
                metadatos,
                Collections.emptyList(),
                Collections.emptyList(),
                anexos
        );
    }

    private Set<Metadato> buildMetadatos(List<MetadatoDocumentoDTO> metadatosDTO) {
        if (metadatosDTO == null || metadatosDTO.isEmpty()) {
            return Collections.emptySet();
        }

        Map<String, String> mapaMetadatos = metadatosDTO.stream()
                .collect(Collectors.toMap(
                        MetadatoDocumentoDTO::nombre,
                        MetadatoDocumentoDTO::valor
                ));

        var definiciones = definicionMetadatosRepository.buscarPorPatrones(
                new ArrayList<>(mapaMetadatos.keySet())
        );

        return definiciones.stream()
                .map(d -> d.generarMetadato(mapaMetadatos.get(d.getPatron())))
                .collect(Collectors.toSet());
    }

    private Anexo buildAnexo(AnexoDocumentoDTO anexoDTO) {
        return Anexo.builder()
                .nombre(anexoDTO.nombre())
                .descripcion(anexoDTO.descripcion())
                .fileId(anexoDTO.fileId())
                .hash(anexoDTO.hash())
                .bytes(anexoDTO.bytes())
                .extension(anexoDTO.extension())
                .build();
    }

    private Consecutivo obtenerConsecutivoRecepcion() {
        Consecutivo consecutivoEjemplo = new Consecutivo(
                "REC", 
                String.valueOf(Calendar.getInstance().get(Calendar.YEAR)), 
                0, 
                TipoConsecutivo.RECEPCION
        );

        return consecutivoRepository.findByExample(consecutivoEjemplo)
                .orElseGet(() -> {
                    Consecutivo nuevoConsecutivo = new Consecutivo(
                            "REC", 
                            String.valueOf(Calendar.getInstance().get(Calendar.YEAR)), 
                            0, 
                            TipoConsecutivo.RECEPCION
                    );
                    return consecutivoRepository.save(nuevoConsecutivo);
                });
    }

    private ExternalUser obtenerRemitente(String remitenteId) {
        if (remitenteId == null) {
            throw new IllegalArgumentException("El remitente es requerido");
        }
        return externalUsersRepository.findById(UUID.fromString(remitenteId))
                .orElseThrow(() -> new EntityNotExistsException("Remitente no encontrado"));
    }

    private Seccion obtenerDestino(String destinoId) {
        if (destinoId == null) {
            throw new IllegalArgumentException("El destino es requerido");
        }
        return seccionRepository.findById(UUID.fromString(destinoId))
                .orElseThrow(() -> new EntityNotExistsException("Destino no encontrado"));
    }

    private UsuarioSeccion obtenerDestinatarioInterno(String destinatarioInternoId, Seccion destino) {
        if (destinatarioInternoId == null) {
            // Si no se especifica destinatario interno, buscar el responsable primario de la sección
            return destino.getUsuarios()
                .stream()
                .filter(u -> u.getUsername().equals(destino.getResponsable()))
                .findFirst()
                .orElse(null);
        }

        // Buscar el usuario específico en la sección destino
        return destino.getUsuarios().stream()
                .filter(u -> u.getUsername().equals(destinatarioInternoId))
                .findFirst()
                .orElseThrow(() -> new EntityNotExistsException("Destinatario interno no encontrado en la sección destino"));
    }
}
