package co.com.gedsys.base.adapter.http.produccion.documentos;

import co.com.gedsys.base.application.dto.AnexoDocumentoDTO;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.PropiedadRadicadoDTO;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.usecase.produccion.documental.*;
import co.com.gedsys.base.application.usecase.produccion.documental.command.*;
import co.com.gedsys.base.application.usecase.produccion.radicacion.RadicarDocumentoEnvioDocumentalUseCase;
import co.com.gedsys.base.application.usecase.produccion.radicacion.RadicarDocumentoProducidoUseCase;
import co.com.gedsys.base.application.usecase.produccion.radicacion.RadicarDocumentoRecibidoUseCase;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoEnvioCommand;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoProducidoCommand;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoRecibidoCommand;
import co.com.gedsys.base.application.usecase.produccion.documental.CargarDocumentoUseCase;
import co.com.gedsys.base.application.usecase.produccion.documental.CargarDocumentoCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequiredArgsConstructor
public class ProduccionDocumentalController implements ProduccionDocumentosAPI {

    private final RegistrarBorradorUseCase createUseCase;
    private final ActualizarBorradorUseCase actualizarBorradorUseCase;
    private final FirmarDocumentoUseCase firmarDocumentoUseCase;
    private final RechazarFirmaUseCase rechazarFirmaDocumentoUseCase;
    private final AprobarDocumentoUseCase aprobarDocumentoUseCase;
    private final DesaprobarDocumentoUseCase desaprobarDocumentoUseCase;
    @org.springframework.beans.factory.annotation.Qualifier("produccionDocumentosHttpMapperImpl")
    private final ProduccionDocumentosHttpMapper mapper;
    private final RadicarDocumentoProducidoUseCase radicarDocumentoProducidoUseCase;
    private final RadicarDocumentoRecibidoUseCase recibirDocumentoUseCase;
    private final RadicarDocumentoEnvioDocumentalUseCase radicarDocumentoEnvioDocumentalUseCase;
    private final CargarDocumentoUseCase cargarDocumentoUseCase;

    @Override
    public RespuestaDocumentoProducido crearBorrador(SolicitudRegistroDocumento request) {
        var command = mapper.toCommand(request);
        var document = createUseCase.execute(command);
        return mapper.toResponse(document);
    }

    @Override
    public ResponseEntity<RespuestaDocumentoProducido> actualizarBorrador(UUID id,
            SolicitudActualizacionDocumento request) {
        var command = new UpdateDraftCommand(
                id,
                request.titulo(),
                request.metadatos(),
                request.anexos().stream()
                        .map(mapper::toCommandAnexo)
                        .toList());
        var documento = actualizarBorradorUseCase.execute(command);
        return ResponseEntity.ok(mapper.toResponse(documento));
    }

    @Override
    public ResponseEntity<Void> firmarDocumento(UUID id, String firmante) {
        firmarDocumentoUseCase.execute(new FirmarDocumentoCommand(id, firmante));
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> rechazarFirmaDocumento(UUID id, SolicitudRechazoFirmaDocumento request,
            String firmante) {
        rechazarFirmaDocumentoUseCase.execute(new RechazarFirmaCommand(id, firmante, request.observaciones()));
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> aprobarDocumento(UUID id, String aprobador) {
        aprobarDocumentoUseCase.execute(new AprobarDocumentoCommand(id, aprobador));
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> desaprobarDocumento(UUID id, SolicitudDesaprobarDocumento request, String aprobador) {
        desaprobarDocumentoUseCase.execute(new DesaprobarDocumentoCommand(id, aprobador, request.observaciones()));
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<RadicadoDTO> radicarDocumento(UUID documentId) {
        var command = new RadicarDocumentoProducidoCommand(documentId);
        var radicado = radicarDocumentoProducidoUseCase.execute(command);
        return ResponseEntity.ok(radicado);
    }

    @Override
    public ResponseEntity<RadicadoDTO> recibirDocumento(SolicitudRecepcionDocumento request) {
        var command = new RadicarDocumentoRecibidoCommand(
                request.titulo(),
                request.fileId(),
                request.tipoDocumentalId(),
                request.unidadDocumentalId(),
                request.autor(),
                request.metadatos().stream()
                        .map(m -> new MetadatoDocumentoDTO(m.nombre(), m.valor()))
                        .toList(),
                new PropiedadRadicadoDTO(
                        request.propiedadesRadicado().height(),
                        request.propiedadesRadicado().page(),
                        request.propiedadesRadicado().width(),
                        request.propiedadesRadicado().x(),
                        request.propiedadesRadicado().y(),
                        request.propiedadesRadicado().rotationDegrees()),
                request.anexos().stream()
                        .map(a -> new AnexoDocumentoDTO(
                                a.nombre(),
                                a.descripcion(),
                                a.fileId(),
                                a.hash(),
                                a.bytes(),
                                a.extension()))
                        .toList(),
                request.remitenteId(),
                request.destinoId(),
                request.destinatarioInternoId());
        var radicado = recibirDocumentoUseCase.execute(command);
        return ResponseEntity.ok(radicado);
    }

    @Override
    public ResponseEntity<RadicadoDTO> radicarDocumentoEnvioDocumental(UUID documentId, SolicitudDeEnvio request, String emisorUsername) {
        List<MetadatoDocumentoDTO> metadatos = List.of();

        if (request.metadatos() != null) {
            metadatos = request.metadatos().stream()
                    .map(m -> new MetadatoDocumentoDTO(m.nombre(), m.valor()))
                    .toList();
        }

        PropiedadRadicadoDTO propiedadRadicadoDTO = null;
        if (request.propiedadesRadicado() != null) {
            var p = request.propiedadesRadicado();
            propiedadRadicadoDTO = new PropiedadRadicadoDTO(
                    p.height(),
                    p.page(),
                    p.width(),
                    p.x(),
                    p.y(),
                    p.rotationDegrees());
        }
        var command = new RadicarDocumentoEnvioCommand(documentId, request.destinatarioId(), emisorUsername, metadatos,
                propiedadRadicadoDTO);
        var radicado = radicarDocumentoEnvioDocumentalUseCase.execute(command);
        return ResponseEntity.ok(radicado);
    }

    @Override
    public RespuestaDocumentoProducido cargarDocumento(SolicitudRegistroDocumento request) {
        if (request.firmas() != null && !request.firmas().isEmpty()) {
            throw new IllegalArgumentException("No se permiten firmas al cargar un documento");
        }
        if (request.aprobaciones() != null && !request.aprobaciones().isEmpty()) {
            throw new IllegalArgumentException("No se permiten aprobaciones al cargar un documento");
        }
        CargarDocumentoCommand command = mapper.toCargarDocumentoCommand(request);
        var documentoDTO = cargarDocumentoUseCase.execute(command);
        return mapper.toResponse(documentoDTO);
    }

}
