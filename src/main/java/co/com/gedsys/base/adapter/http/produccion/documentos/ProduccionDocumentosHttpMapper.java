package co.com.gedsys.base.adapter.http.produccion.documentos;

import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.usecase.produccion.documental.command.RegistrarBorradorCommand;
import co.com.gedsys.base.application.usecase.produccion.documental.command.UpdateDraftCommand;
import co.com.gedsys.base.application.usecase.produccion.documental.CargarDocumentoCommand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ProduccionDocumentosHttpMapper {

    @Mapping(target = "unidadDocumentalNombre", source = "unidadDocumental.nombre")
    @Mapping(target = "unidadDocumentalId", source = "unidadDocumental.id")
    @Mapping(target = "tipoDocumentalNombre", source = "tipoDocumental.nombre")
    @Mapping(target = "tipoDocumentalId", source = "tipoDocumental.id")
    @Mapping(target = "codigoClasificatorio", source = "unidadDocumental.codigoClasificacion")
    RespuestaDocumentoProducido toResponse(DocumentoDTO documentoDTO);

    @Mapping(target = "owner", source = "firmante")
    RespuestaDocumentoProducido.FirmaDS toResponse(DocumentoDTO.FirmaDS firma);

    RespuestaDocumentoProducido.AnexoDS toResponse(DocumentoDTO.AnexoDS anexo);

    RegistrarBorradorCommand toCommand(SolicitudRegistroDocumento request);

    UpdateDraftCommand.RegistroAnexo toCommandAnexo(SolicitudActualizacionDocumento.RegistroAnexo anexo);

    @Mapping(target = "id", ignore = true)
    DocumentoDTO.AnexoDS toAnexoDTO(UpdateDraftCommand.RegistroAnexo anexo);

    SolicitudActualizacionDocumento.RegistroAnexo toResponseAnexo(DocumentoDTO.AnexoDS anexo);

    MetadatoDocumentoDTO toMetadatoDTO(EstructuraMetadatoDocumento metadato);

    List<MetadatoDocumentoDTO> toMetadatosDTO(List<EstructuraMetadatoDocumento> metadatos);

    default CargarDocumentoCommand toCargarDocumentoCommand(SolicitudRegistroDocumento request) {
        return new CargarDocumentoCommand(
                request.titulo(),
                request.autor(),
                request.fileId(),
                request.tipoDocumentalId(),
                request.unidadDocumentalId(),
                request.metadatos() == null ? List.of() : request.metadatos().stream()
                        .map(m -> new CargarDocumentoCommand.Metadato(
                                m.nombre(),
                                m.valor(),
                                m.tipo()
                        )).toList(),
                request.anexos() == null ? List.of() : request.anexos().stream()
                        .map(a -> new CargarDocumentoCommand.Anexo(
                                a.nombre(),
                                a.descripcion(),
                                a.fileId(),
                                a.hash(),
                                a.bytes(),
                                a.extension()
                        )).toList()
        );
    }
}
