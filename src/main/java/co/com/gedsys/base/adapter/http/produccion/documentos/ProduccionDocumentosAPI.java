package co.com.gedsys.base.adapter.http.produccion.documentos;

import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.commons.constant.CustomHeaders;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@Tag(name = "Producción de Documentos", description = "API para la gestión de documentos en producción")
@RequestMapping("/api/v1/produccion/documentos")
public interface ProduccionDocumentosAPI {

        @Operation(summary = "Crear borrador de documento", description = "Crea un nuevo documento en estado borrador")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "201", description = "Documento creado exitosamente", content = @Content(schema = @Schema(implementation = RespuestaDocumentoProducido.class))),
                        @ApiResponse(responseCode = "400", description = "Datos de entrada inválidos")
        })
        @ResponseStatus(code = HttpStatus.CREATED)
        @PostMapping(path = "", consumes = "application/json", produces = "application/json")
        RespuestaDocumentoProducido crearBorrador(@Valid @RequestBody SolicitudRegistroDocumento request);

        @Operation(summary = "Actualizar borrador", description = "Actualiza un documento en estado borrador")
        @PatchMapping(path = "/{id}", consumes = "application/json", produces = "application/json")
        ResponseEntity<RespuestaDocumentoProducido> actualizarBorrador(
                        @Parameter(description = "ID del documento") @PathVariable UUID id,
                        @RequestBody SolicitudActualizacionDocumento request);

        @Operation(summary = "Firmar documento", description = "Firma un documento por un usuario autorizado")
        @PostMapping(path = "/{id}/firmar")
        ResponseEntity<Void> firmarDocumento(
                        @Parameter(description = "ID del documento") @PathVariable UUID id,
                        @Parameter(description = "Usuario firmante") @RequestHeader("X-Username") String firmante);

        @Operation(summary = "Rechazar firma de documento", description = "Rechaza la firma de un documento")
        @PostMapping(path = "/{id}/no-firmar")
        ResponseEntity<Void> rechazarFirmaDocumento(
                        @Parameter(description = "ID del documento") @PathVariable UUID id,
                        @RequestBody SolicitudRechazoFirmaDocumento request,
                        @Parameter(description = "Usuario que rechaza") @RequestHeader("X-Username") String firmante);

        @Operation(summary = "Aprobar documento", description = "Aprueba un documento por un usuario autorizado")
        @PostMapping(path = "/{id}/aprobar")
        ResponseEntity<Void> aprobarDocumento(
                        @Parameter(description = "ID del documento") @PathVariable UUID id,
                        @Parameter(description = "Usuario aprobador") @RequestHeader("X-Username") String aprobador);

        @Operation(summary = "Desaprobar documento", description = "Desaprueba un documento previamente aprobado")
        @PostMapping(path = "/{id}/no-aprobar")
        ResponseEntity<Void> desaprobarDocumento(
                        @Parameter(description = "ID del documento") @PathVariable UUID id,
                        @RequestBody SolicitudDesaprobarDocumento request,
                        @Parameter(description = "Usuario que desaprueba") @RequestHeader("X-Username") String aprobador);

        @Operation(summary = "Radicar documento", description = "Radica un documento")
        @PostMapping(path = "/{documentId}/radicar")
        ResponseEntity<RadicadoDTO> radicarDocumento(
                        @Parameter(description = "ID del documento") @PathVariable UUID documentId);

        @Operation(summary = "Registra la recepción documental", description = "Recepcion documental")
        @PostMapping(path = "/recibir")
        ResponseEntity<RadicadoDTO> recibirDocumento(@RequestBody @Valid SolicitudRecepcionDocumento request);

        @Operation(summary = "Radicar documento de envío", description = "Radica un documento de envío con destinatario")
        @PostMapping(path = "/{documentId}/radicar-envio-documental", consumes = "application/json", produces = "application/json")
        ResponseEntity<RadicadoDTO> radicarDocumentoEnvioDocumental(
                        @Parameter(description = "ID del documento") @PathVariable UUID documentId,
                        @Valid @RequestBody SolicitudDeEnvio request,
                        @Parameter(description = "Usuario emisor") @RequestHeader(CustomHeaders.USERNAME) String emisorUsername);

        @Operation(summary = "Cargar documento", description = "Carga un nuevo documento en el sistema. Las listas de firmas y aprobaciones deben ser vacías.")
        @ApiResponses(value = {
                @ApiResponse(responseCode = "201", description = "Documento cargado exitosamente", content = @Content(schema = @Schema(implementation = RespuestaDocumentoProducido.class))),
                @ApiResponse(responseCode = "400", description = "Datos de entrada inválidos")
        })
        @ResponseStatus(code = HttpStatus.CREATED)
        @PostMapping(path = "/cargar", consumes = "application/json", produces = "application/json")
        RespuestaDocumentoProducido cargarDocumento(@Valid @RequestBody SolicitudRegistroDocumento request);

}
