package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.infrastructure.data_access.RadicadoEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        uses = {DataAccessConsecutivoMapper.class,
                DataAccessClasificacionDocumentalMapper.class,
                DataAccessUnidadDocumentalMapper.class,
                DefinicionMetadatosDataAccessMapper.class,
                DocumentoDataAccessMapper.class,
                DataAccessTipoDocumentalMapper.class,
                ExternalUsersGatewayMapper.class,
                SeccionPersistenceMapper.class},
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface DataAccessRadicadoMapper {

    @Mapping(target = "propiedadesRadicado", source = "entity")
    @Mapping(target = "documento", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    @Mapping(target = "consecutivo", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    @Mapping(target = "destinatario", source = "destinatario")
    @Mapping(target = "remitente", source = "remitente")
    @Mapping(target = "destino", source = "destino")
    @Mapping(target = "destinatarioInterno", source = "destinatarioInterno")
    @Mapping(target = "destino.hijos", ignore = true)
    @Mapping(target = "destino.usuarios", ignore = true)
    @Mapping(target = "destino.padre", ignore = true)
    @Mapping(target = "destinatarioInterno.seccion.hijos", ignore = true)
    @Mapping(target = "destinatarioInterno.seccion.usuarios", ignore = true)
    @Mapping(target = "destinatarioInterno.seccion.padre", ignore = true)
    @Mapping(target = "remitenteInterno.seccion.hijos", ignore = true)
    @Mapping(target = "remitenteInterno.seccion.usuarios", ignore = true)
    @Mapping(target = "remitenteInterno.seccion.padre", ignore = true)
    @Mapping(target = "expedir", ignore = true)
    Radicado toDomain(RadicadoEntity entity);

    @Mapping(target = "propY", source = "propiedadesRadicado.y")
    @Mapping(target = "propX", source = "propiedadesRadicado.x")
    @Mapping(target = "propWidth", source = "propiedadesRadicado.width")
    @Mapping(target = "propPage", source = "propiedadesRadicado.page")
    @Mapping(target = "propHeight", source = "propiedadesRadicado.height")
    @Mapping(target = "propRotationDegrees", source = "propiedadesRadicado.rotationDegrees")
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "documentoId", source = "documento.id")
    @Mapping(target = "consecutivoId", source = "consecutivo.id")
    @Mapping(target = "destinatarioId", source = "destinatario.id")
    @Mapping(target = "destinatario", source = "destinatario")
    @Mapping(target = "remitenteId", source = "remitente.id")
    @Mapping(target = "remitente", source = "remitente")
    @Mapping(target = "destinoId", source = "destino.id")
    @Mapping(target = "destino", source = "destino")
    @Mapping(target = "destinatarioInternoId", source = "destinatarioInterno.id")
    @Mapping(target = "destinatarioInterno", source = "destinatarioInterno")
    @Mapping(target = "remitenteInternoId", source = "remitenteInterno.id")
    @Mapping(target = "remitenteInterno", source = "remitenteInterno")
    @Mapping(target = "destino.usuarios", ignore = true)
    @Mapping(target = "destino.padre", ignore = true)
    @Mapping(target = "destinatarioInterno.seccion.usuarios", ignore = true)
    @Mapping(target = "destinatarioInterno.seccion.padre", ignore = true)
    @Mapping(target = "remitenteInterno.seccion.usuarios", ignore = true)
    @Mapping(target = "remitenteInterno.seccion.padre", ignore = true)
    RadicadoEntity toEntity(Radicado domain);
}
