package co.com.gedsys.base.domain.radicado;

import co.com.gedsys.base.domain.common.Default;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus;
import co.com.gedsys.base.domain.usuario_interno.NombreUsuario;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
public class Radicado {
    private UUID id;
    private EstadoRadicado estado;
    private Integer numeroRadicado;
    private LocalDateTime fechaExpedicion;
    private NombreUsuario emisor;
    private String observaciones;
    private String prefijo;
    private String sufijo;
    private TipoConsecutivo tipo;
    private PropiedadesRadicado propiedadesRadicado;
    private ExternalUser destinatario;

    // Nuevos campos para correspondencia (Issue #8)
    private ExternalUser remitente;
    private Seccion destino;
    private UsuarioSeccion destinatarioInterno;

    // Campo adicional para envíos (Issue #10)
    private UsuarioSeccion remitenteInterno;

    private Consecutivo consecutivo;
    private Documento documento;

    public Radicado(Consecutivo c) {
        this(null, EstadoRadicado.INCOMPLETO, null, null, null,
                null, c.getPrefijo(), c.getSufijo(), c.getTipoConsecutivo(), null, null, null, null, null, null, c, null);
    }

    // Constructor principal (público para compatibilidad con mappers)
    @Default
    public Radicado(UUID id,
                    EstadoRadicado estado,
                    Integer numeroRadicado,
                    LocalDateTime fechaExpedicion,
                    String emisor,
                    String observaciones,
                    String prefijo,
                    String sufijo,
                    TipoConsecutivo tipo,
                    PropiedadesRadicado propiedadesRadicado,
                    ExternalUser destinatario,
                    ExternalUser remitente,
                    Seccion destino,
                    UsuarioSeccion destinatarioInterno,
                    UsuarioSeccion remitenteInterno,
                    Consecutivo consecutivo,
                    Documento documento) {
        this.id = id;
        this.estado = estado;
        this.numeroRadicado = numeroRadicado;
        this.fechaExpedicion = fechaExpedicion;
        this.emisor = new NombreUsuario(emisor);
        this.observaciones = observaciones;
        this.prefijo = prefijo;
        this.sufijo = sufijo;
        this.tipo = tipo;
        this.propiedadesRadicado = propiedadesRadicado;
        this.destinatario = destinatario;
        this.remitente = remitente;
        this.destino = destino;
        this.destinatarioInterno = destinatarioInterno;
        this.remitenteInterno = remitenteInterno;
        this.consecutivo = consecutivo;
        this.documento = documento;
    }

    public Radicado expedir(Documento documento) {
        this.documento = documento;
        if (documento != null && documento.getAutor() != null) {
            setEmisor(documento.getAutor());
        }

        // Validar que los radicados de tipo ENVÍO tengan un destinatario
        validarDestinatarioParaEnvio();

        fechaExpedicion = LocalDateTime.now();
        estado = EstadoRadicado.ACTIVO;
        if (consecutivo != null) {
            numeroRadicado = consecutivo.incrementarContador();
        }
        return this;
    }

    public Radicado expedirConEmisor(Documento documento, String emisorUsername) {
        this.documento = documento;

        // NUEVO: usar emisorUsername en lugar de documento.getAutor()
        if (emisorUsername != null) {
            setEmisor(emisorUsername);
        }

        validarDestinatarioParaEnvio();
        fechaExpedicion = LocalDateTime.now();
        estado = EstadoRadicado.ACTIVO;
        if (consecutivo != null) {
            numeroRadicado = consecutivo.incrementarContador();
        }
        return this;
    }

    public void anular() {
        estado = EstadoRadicado.ANULADO;
    }

    public void setEmisor(String emisor) {
        this.emisor = new NombreUsuario(emisor);
    }

    public String getEmisor() {
        return emisor != null ? emisor.getValue() : null;
    }

    public void setDestinatario(ExternalUser destinatario) {
        if (tipo != TipoConsecutivo.ENVIO) {
            throw new IllegalStateException("Solo los radicados de tipo ENVÍO pueden tener destinatarios");
        }

        if (destinatario == null) {
            throw new IllegalArgumentException("El destinatario no puede ser nulo para radicados de tipo ENVÍO");
        }

        if (destinatario.getStatus() != ExternalUserStatus.ACTIVO) {
            throw new IllegalArgumentException("Solo los usuarios externos con estado ACTIVO pueden ser destinatarios");
        }

        this.destinatario = destinatario;
    }

    public void setRemitenteInterno(UsuarioSeccion remitenteInterno) {
        if (tipo != TipoConsecutivo.ENVIO) {
            throw new IllegalStateException("Solo los radicados de tipo ENVÍO pueden tener remitente interno");
        }

        if (remitenteInterno == null) {
            throw new IllegalArgumentException("El remitente interno no puede ser nulo para radicados de tipo ENVÍO");
        }

        this.remitenteInterno = remitenteInterno;
    }

    public UsuarioSeccion getRemitenteInterno() {
        return remitenteInterno;
    }

    public void setPropiedadesRadicado(PropiedadesRadicado propiedadesRadicado) {
        this.propiedadesRadicado = propiedadesRadicado;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public void setEstado(EstadoRadicado estado) {
        this.estado = estado;
    }

    public void setNumeroRadicado(Integer numeroRadicado) {
        this.numeroRadicado = numeroRadicado;
    }

    public void setDocumento(Documento documento) {
        this.documento = documento;
    }

    public void validarDestinatarioParaEnvio() {
        if (tipo == TipoConsecutivo.ENVIO && destinatario == null) {
            throw new IllegalStateException("Un radicado de tipo ENVÍO debe tener un destinatario");
        }
    }

    public void validarCorrespondenciaParaRecepcion() {
        if (tipo == TipoConsecutivo.RECEPCION) {
            if (remitente == null) {
                throw new IllegalStateException("Un radicado de RECEPCIÓN debe tener un remitente");
            }
            if (destino == null) {
                throw new IllegalStateException("Un radicado de RECEPCIÓN debe tener un destino");
            }
            if (remitente.getStatus() != ExternalUserStatus.ACTIVO) {
                throw new IllegalArgumentException("El remitente debe estar activo");
            }
        }
    }

    // ========== FACTORY METHODS ==========

    /**
     * Factory method para crear radicados de RECEPCIÓN.
     * Usa: remitente (ExternalUser) + destino (Seccion) + destinatarioInterno (UsuarioSeccion)
     */
    public static Radicado forRecepcion(Consecutivo consecutivo,
                                       ExternalUser remitente,
                                       Seccion destino,
                                       UsuarioSeccion destinatarioInterno) {
        validateRecepcionParams(remitente, destino, destinatarioInterno);

        return new Radicado(
            null, EstadoRadicado.INCOMPLETO, null, null, null, null,
            consecutivo.getPrefijo(), consecutivo.getSufijo(), TipoConsecutivo.RECEPCION,
            null,
            null, // destinatario (ExternalUser) - no aplica para recepción
            remitente, destino, destinatarioInterno,
            null, // remitenteInterno - no aplica para recepción
            consecutivo, null
        );
    }

    /**
     * Factory method para crear radicados de ENVÍO.
     * Usa: remitenteInterno (UsuarioSeccion) + destinatario (ExternalUser)
     */
    public static Radicado forEnvio(Consecutivo consecutivo,
                                   UsuarioSeccion remitenteInterno,
                                   ExternalUser destinatario) {
        validateEnvioParams(remitenteInterno, destinatario);

        return new Radicado(
            null, EstadoRadicado.INCOMPLETO, null, null, null, null,
            consecutivo.getPrefijo(), consecutivo.getSufijo(), TipoConsecutivo.ENVIO,
            null, destinatario,
            null, null, null, // campos de recepción - no aplican
            remitenteInterno,
            consecutivo, null
        );
    }

    // ========== MÉTODOS DE VALIDACIÓN ==========

    private static void validateRecepcionParams(ExternalUser remitente,
                                               Seccion destino,
                                               UsuarioSeccion destinatarioInterno) {
        if (remitente == null) {
            throw new IllegalArgumentException("El remitente es requerido para radicados de recepción");
        }
        if (destino == null) {
            throw new IllegalArgumentException("El destino es requerido para radicados de recepción");
        }
        if (destinatarioInterno == null) {
            throw new IllegalArgumentException("El destinatario interno es requerido para radicados de recepción");
        }
        if (remitente.getStatus() != ExternalUserStatus.ACTIVO) {
            throw new IllegalArgumentException("Solo usuarios externos activos pueden ser remitentes");
        }
    }

    private static void validateEnvioParams(UsuarioSeccion remitenteInterno,
                                           ExternalUser destinatario) {
        if (remitenteInterno == null) {
            throw new IllegalArgumentException("El remitente interno es requerido para radicados de envío");
        }
        if (destinatario == null) {
            throw new IllegalArgumentException("El destinatario es requerido para radicados de envío");
        }
        if (destinatario.getStatus() != ExternalUserStatus.ACTIVO) {
            throw new IllegalArgumentException("Solo usuarios externos activos pueden ser destinatarios");
        }
        if (remitenteInterno.getRelacion() != TipoRelacionUsuarioSeccion.PRIMARIA) {
            throw new IllegalArgumentException("Solo usuarios con relación primaria pueden ser remitentes");
        }
    }
}
