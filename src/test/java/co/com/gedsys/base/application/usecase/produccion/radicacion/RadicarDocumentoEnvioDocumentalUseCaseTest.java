package co.com.gedsys.base.application.usecase.produccion.radicacion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoEnvioCommand;
import co.com.gedsys.base.application.usecase.produccion.radicacion.exception.MetadatoNoExisteException;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.Metadato;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

import co.com.gedsys.base.application.usecase.produccion.radicacion.exception.PoliticaUnicoRadicadoDeEnvioException;

@ExtendWith(MockitoExtension.class)
class RadicarDocumentoEnvioDocumentalUseCaseTest {

    @Mock
    private DocumentoRepository documentoRepository;
    @Mock
    private ConsecutivoRepository consecutivoRepository;
    @Mock
    private RadicadoRepository radicadoRepository;
    @Mock
    private ExternalUsersRepository externalUsersRepository;
    @Mock
    private DefinicionMetadatosRepository definicionMetadatosRepository;
    @Mock
    private RadicadoApplicationLayerMapper radicadoMapper;

    @InjectMocks
    private RadicarDocumentoEnvioDocumentalUseCase useCase;

    @BeforeEach
    void setUp() {
        // Inicialización común solo si es estrictamente necesaria
    }

    @Test
    @DisplayName("Debe generar un nuevo radicado de envío correctamente")
    void deberiaCrearUnRadicado() {
        UUID documentoId = UUID.randomUUID();
        UUID destinatarioId = UUID.randomUUID();
        List<MetadatoDocumentoDTO> metadatos = List.of();
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(documentoId, destinatarioId, metadatos, null);
        Documento documento = mock(Documento.class);
        Consecutivo consecutivo = mock(Consecutivo.class);
        when(consecutivo.getTipoConsecutivo()).thenReturn(co.com.gedsys.base.domain.consecutivo.TipoConsecutivo.ENVIO);
        ExternalUser destinatario = mock(ExternalUser.class);
        when(destinatario.getStatus()).thenReturn(co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus.ACTIVO);
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
        when(consecutivoRepository.findByExample(any())).thenReturn(Optional.of(consecutivo));
        when(externalUsersRepository.findById(destinatarioId)).thenReturn(Optional.of(destinatario));
        when(definicionMetadatosRepository.buscarPorPatrones(anyList())).thenReturn(Set.of());

        // Simular que el radicado se genera y se expide correctamente
        Radicado radicado = mock(Radicado.class);
        when(radicadoRepository.save(any())).thenReturn(radicado);
        when(radicadoMapper.toDTO(any())).thenReturn(mock(RadicadoDTO.class));

        // Ejecutar y verificar que no lanza excepción
        assertDoesNotThrow(() -> useCase.execute(command));
    }

    @Test
    @DisplayName("Debe buscar el consecutivo de envío para el tipo ENVIO y crearlo si no existe")
    void deberiaBuscarElConsecutivoDeEnvio() {
        UUID documentoId = UUID.randomUUID();
        UUID destinatarioId = UUID.randomUUID();
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(documentoId, destinatarioId, List.of(), null);
        Documento documento = mock(Documento.class);
        Consecutivo consecutivo = mock(Consecutivo.class);
        when(consecutivo.getTipoConsecutivo()).thenReturn(co.com.gedsys.base.domain.consecutivo.TipoConsecutivo.ENVIO);
        ExternalUser destinatario = mock(ExternalUser.class);
        when(destinatario.getStatus()).thenReturn(co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus.ACTIVO);
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
        when(consecutivoRepository.findByExample(any())).thenReturn(Optional.of(consecutivo));
        when(externalUsersRepository.findById(destinatarioId)).thenReturn(Optional.of(destinatario));

        useCase.execute(command);

        verify(consecutivoRepository).findByExample(any());
        verify(consecutivoRepository, never()).save(any());
    }

    @Test
    @DisplayName("Debe lanzar excepción si el destinatario no existe")
    void deberiaLanzarExcepcionSiDestinatarioNoExiste() {
        UUID documentoId = UUID.randomUUID();
        UUID destinatarioId = UUID.randomUUID();
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(documentoId, destinatarioId, List.of(), null);
        Documento documento = mock(Documento.class);
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
        when(consecutivoRepository.findByExample(any())).thenReturn(Optional.of(mock(Consecutivo.class)));
        when(externalUsersRepository.findById(destinatarioId)).thenReturn(Optional.empty());

        assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
    }

    @Test
    @DisplayName("Debe lanzar excepción si faltan definiciones de metadatos requeridos")
    void deberiaLanzarExcepcionSiFaltanDefinicionesDeMetadatos() {
        UUID documentoId = UUID.randomUUID();
        UUID destinatarioId = UUID.randomUUID();
        List<MetadatoDocumentoDTO> metadatos = List.of(
                new MetadatoDocumentoDTO("obligatorio1", "valor1"),
                new MetadatoDocumentoDTO("obligatorio2", "valor2")
        );
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(documentoId, destinatarioId, metadatos, null);
        Documento documento = mock(Documento.class);
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
        when(consecutivoRepository.findByExample(any())).thenReturn(Optional.of(mock(Consecutivo.class)));
        when(externalUsersRepository.findById(destinatarioId)).thenReturn(Optional.of(mock(ExternalUser.class)));
        // Solo existe la definición para "obligatorio1"
        when(definicionMetadatosRepository.buscarPorPatrones(anyList())).thenReturn(Set.of(mockDefinicionMetadato("obligatorio1")));

        assertThrows(MetadatoNoExisteException.class, () -> useCase.execute(command));
    }

    @Test
    @DisplayName("Debe agregar metadatos adicionales al documento antes de radicar")
    void deberiaAgregarMetadatosAdicionalesAlDocumento() {
        UUID documentoId = UUID.randomUUID();
        UUID destinatarioId = UUID.randomUUID();
        // Crear metadato existente real
        DefinicionMetadato defExistente = DefinicionMetadato.create("existente", TipoMetadatoEnum.CONTENIDO, FormatoMetadatoEnum.ALFANUMERICO);
        Metadato metadatoExistente = defExistente.generarMetadato("valor1");
        Set<Metadato> metadatosDocumento = new HashSet<>(List.of(metadatoExistente));
        // Crear documento real
        Documento documento = new Documento(
                documentoId,
                "Titulo",
                "fileId",
                null,
                null,
                null,
                "autor",
                metadatosDocumento,
                List.of(),
                List.of(),
                List.of(),
                List.of()
        );
        // El comando trae uno existente y uno nuevo
        List<MetadatoDocumentoDTO> metadatosComando = List.of(
                new MetadatoDocumentoDTO("existente", "valor1"),
                new MetadatoDocumentoDTO("nuevo", "valor2")
        );
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(documentoId, destinatarioId, metadatosComando, null);
        Consecutivo consecutivo = mock(Consecutivo.class);
        when(consecutivo.getTipoConsecutivo()).thenReturn(TipoConsecutivo.ENVIO);
        ExternalUser destinatario = mock(ExternalUser.class);
        when(destinatario.getStatus()).thenReturn(ExternalUserStatus.ACTIVO);
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
        when(consecutivoRepository.findByExample(any())).thenReturn(Optional.of(consecutivo));
        when(externalUsersRepository.findById(destinatarioId)).thenReturn(Optional.of(destinatario));
        // Definiciones para ambos metadatos (reales)
        DefinicionMetadato defNuevo = DefinicionMetadato.create("nuevo", TipoMetadatoEnum.CONTENIDO, FormatoMetadatoEnum.ALFANUMERICO);
        when(definicionMetadatosRepository.buscarPorPatrones(anyList())).thenReturn(Set.of(defExistente, defNuevo));
        // Simular persistencia y mapeo
        Radicado radicado = mock(Radicado.class);
        when(radicadoRepository.save(any())).thenReturn(radicado);
        when(radicadoMapper.toDTO(any())).thenReturn(mock(RadicadoDTO.class));

        useCase.execute(command);

        // Verifica que el documento contiene ambos metadatos
        Set<String> nombres = documento.getMetadatos().stream().map(Metadato::nombre).collect(Collectors.toSet());
        assertTrue(nombres.contains("existente"));
        assertTrue(nombres.contains("nuevo"));
    }

    @Test
    @DisplayName("Debe lanzar excepción si el documento ya tiene un radicado de envío activo")
    void deberiaLanzarExcepcionSiYaExisteRadicadoEnvioActivo() {
        UUID documentoId = UUID.randomUUID();
        UUID destinatarioId = UUID.randomUUID();

        // Crear documento real
        Documento documento = new Documento(
                documentoId,
                "Titulo",
                "fileId",
                null,
                null,
                null,
                "autor",
                new HashSet<>(),
                new ArrayList<>(),
                new ArrayList<>(),
                new ArrayList<>(),
                new ArrayList<>()
        );

        // Crear radicado de envío activo y agregarlo al documento
        Consecutivo consecutivoEnvio = new Consecutivo("ENV", "2024", 0, TipoConsecutivo.ENVIO);
        Radicado radicadoEnvioActivo = new Radicado(
                UUID.randomUUID(),
                co.com.gedsys.base.domain.radicado.EstadoRadicado.ACTIVO,
                1,
                null,
                "autor",
                null,
                "ENV",
                "2024",
                TipoConsecutivo.ENVIO,
                null,
                null,
                null, // remitente
                null, // destino
                null, // destinatarioInterno
                consecutivoEnvio,
                documento
        );
        documento.getRadicados().add(radicadoEnvioActivo);

        // Configurar mocks estrictamente necesarios
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
        // No es necesario stubear consecutivoRepository, externalUsersRepository ni definicionMetadatosRepository porque la excepción se lanza antes
        RadicarDocumentoEnvioCommand command = new RadicarDocumentoEnvioCommand(documentoId, destinatarioId, List.of(), null);

        assertThrows(PoliticaUnicoRadicadoDeEnvioException.class, () -> useCase.execute(command));
    }

    private DefinicionMetadato mockDefinicionMetadato(String nombre) {
        // El nombre es usado como patrón en la lógica de validación
        return new DefinicionMetadato(nombre, TipoMetadatoEnum.CONTENIDO, FormatoMetadatoEnum.ALFANUMERICO);
    }

}
